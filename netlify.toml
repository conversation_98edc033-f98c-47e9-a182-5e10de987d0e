# Netlify configuration for SSL and SEO optimization
[build]
  publish = "dist"
  command = "npm run build"

# Environment variables for production
[build.environment]
  NODE_VERSION = "18"
  NPM_VERSION = "9"

# Headers for security and SEO
[[headers]]
  for = "/*"
  [headers.values]
    # Security headers for SSL/HTTPS
    Strict-Transport-Security = "max-age=31536000; includeSubDomains; preload"
    X-Content-Type-Options = "nosniff"
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    Referrer-Policy = "strict-origin-when-cross-origin"
    
    # Content Security Policy for enhanced security
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google.com https://www.gstatic.com https://maps.googleapis.com https://js.mollie.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https: blob:; connect-src 'self' https://bbyifnpcqefabwexvxdc.supabase.co https://api.mollie.com https://www.google-analytics.com; frame-src https://www.google.com https://js.mollie.com; object-src 'none'; base-uri 'self';"
    
    # Cache control for performance
    Cache-Control = "public, max-age=31536000, immutable"

# Specific headers for HTML files (SEO optimization)
[[headers]]
  for = "/*.html"
  [headers.values]
    Cache-Control = "public, max-age=0, must-revalidate"
    X-Robots-Tag = "index, follow"

# Headers for static assets
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Headers for images
[[headers]]
  for = "/*.{png,jpg,jpeg,gif,webp,svg,ico}"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# HTTPS redirects - Force all HTTP traffic to HTTPS
[[redirects]]
  from = "http://klusgebied.nl/*"
  to = "https://klusgebied.nl/:splat"
  status = 301
  force = true

[[redirects]]
  from = "http://www.klusgebied.nl/*"
  to = "https://klusgebied.nl/:splat"
  status = 301
  force = true

[[redirects]]
  from = "http://admin.klusgebied.nl/*"
  to = "https://admin.klusgebied.nl/:splat"
  status = 301
  force = true

[[redirects]]
  from = "https://www.klusgebied.nl/*"
  to = "https://klusgebied.nl/:splat"
  status = 301
  force = true

# SPA fallback for React Router
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Domain-specific redirects for admin subdomain
[[redirects]]
  from = "https://klusgebied.nl/beheerder/*"
  to = "https://admin.klusgebied.nl/:splat"
  status = 301

# SSL/TLS settings
[context.production]
  environment = { NODE_ENV = "production" }

# Branch-specific settings
[context.branch-deploy]
  command = "npm run build:dev"

# Plugin for automatic SSL certificate management
[[plugins]]
  package = "@netlify/plugin-lighthouse"

# Form handling (if needed)
[build.processing]
  skip_processing = false

[build.processing.css]
  bundle = true
  minify = true

[build.processing.js]
  bundle = true
  minify = true

[build.processing.html]
  pretty_urls = true

# Edge functions for enhanced security (optional)
[[edge_functions]]
  function = "security-headers"
  path = "/*"
